
import React, { useState, useRef, useEffect } from 'react';
import {
  PageContainer,
  ActionType
} from '@ant-design/pro-components';
import {
  Card,
  Row,
  Col,
  Statistic,
  Button,
  Space,
  Typography,
  Modal,
  message,
  Tabs,
} from 'antd';
import {
  PlusOutlined,
  UserOutlined,
  SettingOutlined
} from '@ant-design/icons';

import WorkPatternModal from '@/pages/Scheduling/components/WorkPatternModal';
import WorkSchedulingManagement from '@/pages/Scheduling/components/WorkSchedulingManagement';
import WorkPatternManagement from '@/pages/Scheduling/components/WorkPatternManagement';
import {deleteUsingPost, listUsingPost2} from "@/services/logistics-backend-api/base/pattern/workPatternController";
import {listUsingPost} from "@/services/logistics-backend-api/base/scheduling/workSchedulingController";
import WorkSchedulingModal from './components/WorkSchedulingModal';
import WorkCalendar from "@/pages/Scheduling/components/WorkCalendar";

const { Title } = Typography;
const { TabPane } = Tabs;

const Scheduling1: React.FC = () => {
  //@ts-ignore
  const actionRef = useRef<ActionType>();
  //@ts-ignore
  const actionRef2 = useRef<ActionType>();
  const [activeTab, setActiveTab] = useState('scheduling');

  // Modal 状态
  const [currentScheduling, setCurrentScheduling] = useState<WORK_SCHEDULING_API.WorkSchedulingVO | undefined>();
  const [schedulingModalVisible, setSchedulingModalVisible] = useState(false);
  //工作模式的当前数据和Modal的展示效果
  const [currentPattern, setCurrentPattern] = useState<WORK_PATTERN_API.WorkPattern | undefined>();
  const [patternModalVisible, setPatternModalVisible] = useState(false);

  // 数据状态
  const [patternsWorkPattern, setWorkPattern] = useState<WORK_PATTERN_API.WorkPattern[]>([]);
  const [schedulings, setSchedulings] = useState<WORK_SCHEDULING_API.WorkSchedulingVO[]>([]);


  // 模拟数据加载
  useEffect(() => {
    // loadMockData();
  }, []);

  return (
    <PageContainer
      title="工作排班管理"
      subTitle="基于工作模式的员工排班管理系统"
    >
      {/* 主要内容区域 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          tabBarExtraContent={
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => setSchedulingModalVisible(true)}
              >
                添加员工排班
              </Button>
              <Button
                icon={<PlusOutlined />}
                onClick={() => setPatternModalVisible(true)}
              >
                添加工作模式
              </Button>
            </Space>
          }
        >
          <TabPane tab="员工排班管理" key="scheduling">
            <WorkSchedulingManagement
              actionRef = {actionRef2}
              patterns={schedulings}
              onEditScheduling={(scheduling) => {
                setCurrentScheduling(scheduling);
                setSchedulingModalVisible(true);
              }}
              onDeleteScheduling={(schedulingId) => {
                Modal.confirm({
                  title: '确认删除',
                  content: '确定要删除这个员工排班吗？',
                  onOk: () => {
                    setSchedulings(prev => prev.filter(s => s.id !== schedulingId));
                    message.success('删除成功');
                  }
                });
              }}
            />
          </TabPane>
          <TabPane tab="工作模式管理" key="patterns">
            <WorkPatternManagement
              patterns={patternsWorkPattern}
              actionRef={actionRef}
              onEditPattern={(pattern) => {
                setCurrentPattern(pattern);
                setPatternModalVisible(true);
              }}
              onDeletePattern={(patternId) => {
                Modal.confirm({
                  title: '确认删除',
                  content: '确定要删除这个工作模式吗？',
                  onOk: async () => {
                    try {
                      await deleteUsingPost({id: patternId})
                      message.success('删除成功');
                    }catch ( e:any){
                      message.error("删除失败!"+e.message)
                    }

                  }
                });
              }}
            />
          </TabPane>
          <TabPane tab="工作日历" key="calendar">
            <WorkCalendar
              schedulings={schedulings}
              patterns={patternsWorkPattern}
              onSchedulingClick={(scheduling) => {
                setCurrentScheduling(scheduling);
                setSchedulingModalVisible(true);
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 模态框 */}
      <WorkSchedulingModal
        visible={schedulingModalVisible}
        scheduling={currentScheduling}
        patterns={ patternsWorkPattern}
        onCancel={() => {
          setSchedulingModalVisible(false);
          setCurrentScheduling(undefined);
        }}
        onSuccess={() => {
          setSchedulingModalVisible(false);
          setCurrentScheduling(undefined);
          actionRef2.current?.reload();
        }}
      />

      <WorkPatternModal
        visible={patternModalVisible}
        pattern={currentPattern}
        onCancel={() => {
          setPatternModalVisible(false);
          setCurrentPattern(undefined);
        }}
        onSuccess={() => {
          setPatternModalVisible(false);
          setCurrentPattern(undefined);
          actionRef.current?.reload(); // 修改这里
        }}
      />
    </PageContainer>
  );
};

export default Scheduling1;

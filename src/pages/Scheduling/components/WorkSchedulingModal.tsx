import React, {useEffect, useState} from 'react';
import {Modal, Form, Input, Select, InputNumber, DatePicker, Row, Col, message, Alert} from 'antd';
import dayjs from 'dayjs';
import {USER_TYPE_MAP} from "@/constants";
import {getAccountUsingGet} from "@/services/logistics-backend-api/author/user/userController";
import {listByUserTypeUsingGet} from "@/services/logistics-backend-api/base/pattern/workPatternController";
import {addUsingPost2} from "@/services/logistics-backend-api/base/scheduling/workSchedulingController";

// 定义用户类型映射

interface WorkSchedulingModalProps {
  visible: boolean;
  scheduling?: WORK_SCHEDULING_API.WorkSchedulingVO;
  patterns: WORK_PATTERN_API.WorkPattern[];
  onCancel: () => void;
  onSuccess: () => void;
}

const {Option} = Select;

const WorkSchedulingModal: React.FC<WorkSchedulingModalProps> = ({
                                                                   visible,
                                                                   scheduling,
                                                                   patterns,
                                                                   onCancel,
                                                                   onSuccess
                                                                 }) => {
  const [form] = Form.useForm();
  const [workPatternsSelectVO, setWorkPatternsSelectVO] = useState<WORK_PATTERN_API.WorkPatternSelectVO[]>([])

  // 根据用户类型加载工作模式
  const loadWorkPatternsByUserType = async (userType: number) => {
    try {
      const {data} = await listByUserTypeUsingGet({userType: userType});
      if (data && Array.isArray(data)) {
        setWorkPatternsSelectVO(data);
        console.log('获取到的工作模式:', data);
      } else {
        setWorkPatternsSelectVO([]);
      }
    } catch (error) {
      console.error('获取工作模式失败:', error);
      setWorkPatternsSelectVO([]);
      message.error('获取工作模式失败');
    }
  };



  useEffect(() => {
    if (visible) {
      if (scheduling) {
        // 编辑模式：先设置基本信息
        form.setFieldsValue({
          ...scheduling,
          workContinueStartTime: scheduling.workContinueStartTime
            ? dayjs(scheduling.workContinueStartTime)
            : undefined,
        });

        // 编辑模式下，根据当前记录的userType加载对应的工作模式
        if (scheduling.userType) {
          loadWorkPatternsByUserType(scheduling.userType).then(() => {
            // 工作模式加载完成后，设置workPatternId
            if (scheduling.workPatternId) {
              form.setFieldsValue({
                workPatternId: scheduling.workPatternId
              });
            }
          });
        }
      } else {
        // 新建模式：重置表单并设置默认值
        form.resetFields();
        setWorkPatternsSelectVO([]); // 清空工作模式选项
        form.setFieldsValue({
          userType: 1,
          state: 1,
        });
      }
    }
  }, [visible, scheduling, form]);

  const handleSubmit = async () => {
    try {
      // 先进行表单验证
      const values = await form.validateFields();

      if (!scheduling) {
        // 新增员工排班
        await addUsingPost2({
          ...values,
          agencyId: userVO?.orgId,
          state: 1,
        } as WORK_SCHEDULING_API.WorkSchedulingAddRequest);
        message.success('创建成功');
      } else {
        // 编辑员工排班 - 这里需要调用编辑接口
        // TODO: 添加编辑接口调用
        message.success('编辑成功');
        console.log('编辑数据:', {...values, id: scheduling.id});
      }

      onSuccess();
    } catch (error: any) {
      message.error(scheduling ? "编辑失败: " + error.message : "创建失败: " + error.message);
    }
  };

  //根据账号去获取UserVO
  const [userVO, setUserVO] = useState<USER_API.UserVO>();

  return (
    <Modal
      title={scheduling ? '编辑员工排班' : '添加员工排班'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="employeeNumber"
              label="员工账号"
              rules={[{required: true, message: '请输入员工账号'}]}
            >
              <Input
                placeholder="请输入员工账号"
                onBlur={async (e) => {
                  const employeeNumber = e.target.value.trim();
                  if (!employeeNumber) return; // 如果账号为空，不执行查询
                  try {
                    console.log('员工账号失去焦点:', employeeNumber);
                    const {data} = await getAccountUsingGet({account: employeeNumber});
                    if (data) {
                      // 将获取到的用户信息赋值到表单
                      form.setFieldsValue({
                        userId: data.id,           // 用户ID
                        name: data.name,           // 姓名
                        phone: data.mobile,         // 电话
                      });
                      setUserVO(data);
                      message.success('员工信息获取成功');
                    } else {
                      message.warning('未找到该员工信息');
                    }
                  } catch (error) {
                    console.error('获取员工信息失败:', error);
                    message.error('获取员工信息失败');
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="userId"
              label="用户ID"
              rules={[{required: true, message: '请输入用户ID'}]}
            >
              <InputNumber
                placeholder="请输入用户ID"
                style={{width: '100%', backgroundColor: '#f5f5f5'}}
                min={1}
                readOnly
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="员工姓名"
              rules={[{required: true, message: '请输入员工姓名'}]}
            >
              <Input
                placeholder="请输入员工姓名"
                style={{width: '100%', backgroundColor: '#f5f5f5'}}
                min={1}
                readOnly
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="phone"
              label="手机号"
              rules={[
                {required: true, message: '请输入手机号'},
              ]}
            >
              <Input
                placeholder="请输入手机号"
                style={{width: '100%', backgroundColor: '#f5f5f5'}}
                min={1}
                readOnly
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="userType"
              label="员工类型"
              rules={[{required: true, message: '请选择员工类型'}]}
            >
              <Select
                placeholder="请选择员工类型"
                onChange={async (userType) => {
                  // 清空工作模式选择
                  form.setFieldsValue({workPatternId: undefined});
                  // 根据用户类型加载对应的工作模式
                  await loadWorkPatternsByUserType(userType);
                }}
              >
                <Option value={1}>员工</Option>
                <Option value={2}>快递员</Option>
                <Option value={3}>司机</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="workPatternId"
              label="工作模式"
              rules={[{required: true, message: '请选择工作模式'}]}
            >
              <Select placeholder="请选择工作模式" allowClear>
                {workPatternsSelectVO.map(pattern => (
                  <Option key={pattern.id} value={pattern.id}>
                    {pattern.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {/* 工作模式详情显示 */}
        <Form.Item shouldUpdate>
          {({getFieldValue}) => {
            const workPatternId = getFieldValue('workPatternId');
            const selectedPattern = patterns.find(p => p.id === workPatternId);

            if (selectedPattern?.workPatternType === 2) {
              return (
                <div>
                  <Alert
                    message="连续制工作模式"
                    description="连续制需要设置开始工作日期，系统将根据工作模式自动计算工作日程。"
                    type="info"
                    style={{marginBottom: '16px'}}
                  />
                  <Form.Item
                    name="workContinueStartTime"
                    label="连续制开始工作日期"
                    rules={[{required: true, message: '请选择连续制开始工作日期'}]}
                  >
                    <DatePicker
                      showTime
                      placeholder="请选择连续制开始工作日期"
                      style={{width: '100%'}}
                      format="YYYY-MM-DD HH:mm:ss"
                    />
                  </Form.Item>
                </div>
              );
            }
            return null;
          }}
        </Form.Item>

        {/* 工作模式详情信息 */}
        <Form.Item shouldUpdate>
          {({getFieldValue}) => {
            const workPatternId = getFieldValue('workPatternId');
            const selectedPattern = patterns.find(p => p.id === workPatternId);

            if (selectedPattern) {
              const minutesToTime = (minutes: number): string => {
                const hours = Math.floor(minutes / 60);
                const mins = minutes % 60;
                return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
              };

              return (
                <Alert
                  message="工作模式详情"
                  description={
                    <div>
                      <p><strong>模式名称：</strong>{selectedPattern.name}</p>
                      <p>
                        <strong>工作时间：</strong>{minutesToTime(selectedPattern.workStartMinute1)} - {minutesToTime(selectedPattern.workEndMinute1)}
                      </p>
                      <p><strong>模式类型：</strong>{selectedPattern.workPatternType === 1 ? '礼拜制' : '连续制'}</p>
                      {selectedPattern.workPatternType === 2 && (
                        <p>
                          <strong>工作安排：</strong>连续工作{selectedPattern.workDayNum}天，休息{selectedPattern.restDayNum}天
                        </p>
                      )}
                    </div>
                  }
                  type="success"
                  style={{marginBottom: '16px'}}
                />
              );
            }
            return null;
          }}
        </Form.Item>

      </Form>
    </Modal>
  );
};

export default WorkSchedulingModal;

import React, {useEffect, useState} from 'react';
import {Modal, Form, Input, Select, InputNumber, DatePicker, Row, Col, message, Alert} from 'antd';
import dayjs from 'dayjs';
import {USER_TYPE_MAP} from "@/constants";
import {getAccountUsingGet} from "@/services/logistics-backend-api/author/user/userController";
import {listByUserTypeUsingGet} from "@/services/logistics-backend-api/base/pattern/workPatternController";
import {addUsingPost2} from "@/services/logistics-backend-api/base/scheduling/workSchedulingController";

// 定义用户类型映射

interface WorkSchedulingModalProps {
  visible: boolean;
  scheduling?: WORK_SCHEDULING_API.WorkSchedulingVO;
  patterns: WORK_PATTERN_API.WorkPattern[];
  onCancel: () => void;
  onSuccess: () => void;
}

const {Option} = Select;

const WorkSchedulingModal: React.FC<WorkSchedulingModalProps> = ({
                                                                   visible,
                                                                   scheduling,
                                                                   patterns,
                                                                   onCancel,
                                                                   onSuccess
                                                                 }) => {
  const [form] = Form.useForm();
  const [workPatternsSelectVO, setWorkPatternsSelectVO] = useState<WORK_PATTERN_API.WorkPatternSelectVO[]>([])

  // 根据用户类型加载工作模式
  const loadWorkPatternsByUserType = async (userType: number) => {
    try {
      const {data} = await listByUserTypeUsingGet({userType: userType});
      if (data && Array.isArray(data)) {
        setWorkPatternsSelectVO(data);
        console.log('获取到的工作模式:', data);
      } else {
        setWorkPatternsSelectVO([]);
      }
    } catch (error) {
      console.error('获取工作模式失败:', error);
      setWorkPatternsSelectVO([]);
      message.error('获取工作模式失败');
    }
  };



  useEffect(() => {
    if (visible) {
      if (scheduling) {
        // 编辑模式：先设置基本信息
        form.setFieldsValue({
          ...scheduling,
          workContinueStartTime: scheduling.workContinueStartTime
            ? dayjs(scheduling.workContinueStartTime)
            : undefined,
        });

        // 编辑模式下，根据当前记录的userType加载对应的工作模式
        if (scheduling.userType) {
          loadWorkPatternsByUserType(scheduling.userType).then(() => {
            // 工作模式加载完成后，设置workPatternId
            if (scheduling.workPatternId) {
              form.setFieldsValue({
                workPatternId: scheduling.workPatternId
              });
            }
          });
        }
      } else {
        // 新建模式：重置表单并设置默认值
        form.resetFields();
        setWorkPatternsSelectVO([]); // 清空工作模式选项
        form.setFieldsValue({
          userType: 1,
          state: 1,
        });
      }
    }
  }, [visible, scheduling, form]);

  const handleSubmit = async () => {
    // 先进行表单验证
    const values = await form.validateFields();
    if (!scheduling) {
      try {
        //新增员工排班
        await addUsingPost2({
          ...await form.validateFields(),
          agencyId: userVO?.orgId,
          state: 1,
        } as WORK_SCHEDULING_API.WorkSchedulingAddRequest)
        message.success('创建成功');
        onSuccess();
      } catch (error: any) {
        message.error("创建失败," + error.message);
        onSuccess();
      }
    }
    console.log('提交表单:', {...values, agencyId: userVO?.orgId});
  };

  //根据账号去获取UserVO
  const [userVO, setUserVO] = useState<USER_API.UserVO>();

  return (
    <Modal
      title={scheduling ? '编辑员工排班' : '添加员工排班'}
      open={visible}
      onCancel={onCancel}
      onOk={handleSubmit}
      width={800}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="employeeNumber"
              label="员工账号"
              rules={[{required: true, message: '请输入员工账号'}]}
            >
              <Input
                placeholder="请输入员工账号"
                onBlur={async (e) => {
                  const employeeNumber = e.target.value.trim();
                  if (!employeeNumber) return; // 如果账号为空，不执行查询
                  try {
                    console.log('员工账号失去焦点:', employeeNumber);
                    const {data} = await getAccountUsingGet({account: employeeNumber});
                    if (data) {
                      // 将获取到的用户信息赋值到表单
                      form.setFieldsValue({
                        userId: data.id,           // 用户ID
                        name: data.name,           // 姓名
                        phone: data.mobile,         // 电话
                      });
                      setUserVO(data);
                      message.success('员工信息获取成功');
                    } else {
                      message.warning('未找到该员工信息');
                    }
                  } catch (error) {
                    console.error('获取员工信息失败:', error);
                    message.error('获取员工信息失败');
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="userId"
              label="用户ID"
              rules={[{required: true, message: '请输入用户ID'}]}
            >
              <InputNumber
                placeholder="请输入用户ID"
                style={{width: '100%', backgroundColor: '#f5f5f5'}}
                min={1}
                readOnly
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="员工姓名"
              rules={[{required: true, message: '请输入员工姓名'}]}
            >
              <Input
                placeholder="请输入员工姓名"
                style={{width: '100%', backgroundColor: '#f5f5f5'}}
                min={1}
                readOnly
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="phone"
              label="手机号"
              rules={[
                {required: true, message: '请输入手机号'},
              ]}
            >
              <Input
                placeholder="请输入手机号"
                style={{width: '100%', backgroundColor: '#f5f5f5'}}
                min={1}
                readOnly
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="userType"
              label="员工类型"
              rules={[{required: true, message: '请选择员工类型'}]}
            >
              <Select
                placeholder="请选择员工类型"
                onChange={async (userType) => {
                  // 清空工作模式选择
                  form.setFieldsValue({workPatternId: undefined});
                  // 根据用户类型加载对应的工作模式
                  await loadWorkPatternsByUserType(userType);
                }}
              >
                <Option value={1}>员工</Option>
                <Option value={2}>快递员</Option>
                <Option value={3}>司机</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="workPatternId"
              label="工作模式"
              rules={[{required: true, message: '请选择工作模式'}]}
            >
              <Select placeholder="请选择工作模式" allowClear>
                {workPatternsSelectVO.map(pattern => (
                  <Option key={pattern.id} value={pattern.id}>
                    {pattern.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

      </Form>
    </Modal>
  );
};

export default WorkSchedulingModal;

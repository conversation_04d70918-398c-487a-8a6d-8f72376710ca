import React, { useState, useMemo } from 'react';
import { Calendar, Badge, Card, Row, Col, Typography, Space, Tag, Button, Select, DatePicker } from 'antd';
import { ClockCircleOutlined, UserOutlined, CalendarOutlined } from '@ant-design/icons';
import dayjs, { Dayjs } from 'dayjs';
import {USER_TYPE_MAP} from "@/constants";
const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

interface WorkCalendarProps {
  schedulings: WORK_PATTERN_API.WorkPattern[];
  patterns: WORK_PATTERN_API.WorkPattern[];
  onSchedulingClick: (scheduling: WORK_SCHEDULING_API.WorkSchedulingVO) => void;
}

const WorkCalendar: React.FC<WorkCalendarProps> = ({
  schedulings,
  patterns,
  onSchedulingClick
}) => {
  const [selectedDate, setSelectedDate] = useState(dayjs());
  const [filterUserType, setFilterUserType] = useState<number | undefined>();
  const [filterPatternId, setFilterPatternId] = useState<number | undefined>();

  // 将分钟数转换为时间格式
  const minutesToTime = (minutes: number): string => {
    console.log('minutes:', minutes)
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // 计算员工在指定日期是否应该工作
  const isWorkingDay = (scheduling: WorkScheduling, date: Dayjs): boolean => {
    if (!scheduling.workPattern) return false;

    const pattern = scheduling.workPattern;

    if (pattern.workPatternType === 1) {
      // 礼拜制
      const dayOfWeek = date.day(); // 0=周日, 1=周一, ..., 6=周六
      const dayMapping = [
        'sunday', 'monday', 'tuesday', 'wednesday',
        'thursday', 'friday', 'saturday'
      ];
      const dayKey = dayMapping[dayOfWeek] as keyof WorkPattern;
      return pattern[dayKey] === 1;
    } else if (pattern.workPatternType === 2 && scheduling.workContinueStartTime) {
      // 连续制
      const startDate = dayjs(scheduling.workContinueStartTime);
      const daysDiff = date.diff(startDate, 'day');

      if (daysDiff < 0) return false;

      const cycleLength = pattern.workDayNum + pattern.restDayNum;
      const positionInCycle = daysDiff % cycleLength;

      return positionInCycle < pattern.workDayNum;
    }

    return false;
  };

  // 获取指定日期的工作安排
  const getWorkSchedulesForDate = (date: Dayjs) => {
    let filteredSchedulings = schedulings.filter(s => s.state === 1);

    // 应用过滤器
    if (filterUserType) {
      filteredSchedulings = filteredSchedulings.filter(s => s.userType === filterUserType);
    }
    if (filterPatternId) {
      filteredSchedulings = filteredSchedulings.filter(s => s.workPatternId === filterPatternId);
    }

    return filteredSchedulings.filter(scheduling => isWorkingDay(scheduling, date));
  };

  // 日历单元格渲染
  const dateCellRender = (value: Dayjs) => {
    const workSchedules = getWorkSchedulesForDate(value);

    return (
      <div style={{ minHeight: '60px' }}>
        {workSchedules.map(scheduling => {
          const userTypeColors = { 1: '#1890ff', 2: '#fa8c16', 3: '#52c41a' };
          const color = userTypeColors[scheduling.userType];

          return (
            <div
              key={scheduling.id}
              style={{
                fontSize: '12px',
                marginBottom: '2px',
                cursor: 'pointer',
                padding: '2px 4px',
                borderRadius: '2px',
                backgroundColor: color + '20',
                border: `1px solid ${color}`,
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap'
              }}
              onClick={() => onSchedulingClick(scheduling)}
            >
              <Badge
                color={color}
                text={`${scheduling.name} (${USER_TYPE_MAP[scheduling.userType]})`}
              />
            </div>
          );
        })}
      </div>
    );
  };

  // 月份单元格渲染
  const monthCellRender = (value: Dayjs) => {
    const monthSchedules = schedulings.filter(scheduling => {
      if (!scheduling.workPattern) return false;

      // 简单统计该月的工作日数量
      const daysInMonth = value.daysInMonth();
      let workDays = 0;

      for (let i = 1; i <= daysInMonth; i++) {
        const date = value.date(i);
        if (isWorkingDay(scheduling, date)) {
          workDays++;
        }
      }

      return workDays > 0;
    });

    if (monthSchedules.length === 0) return null;

    return (
      <div style={{ fontSize: '12px' }}>
        <Badge count={monthSchedules.length} style={{ backgroundColor: '#52c41a' }} />
      </div>
    );
  };

  // 选中日期的详细信息
  const selectedDateSchedules = getWorkSchedulesForDate(selectedDate);

  return (
    <div>
      {/* 过滤器 */}
      <Card size="small" style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col>
            <Text strong>筛选条件：</Text>
          </Col>
          <Col>
            <Select
              placeholder="员工类型"
              allowClear
              style={{ width: 120 }}
              value={filterUserType}
              onChange={setFilterUserType}
            >
              <Option value={1}>员工</Option>
              <Option value={2}>快递员</Option>
              <Option value={3}>司机</Option>
            </Select>
          </Col>
          <Col>
            <Select
              placeholder="工作模式"
              allowClear
              style={{ width: 200 }}
              value={filterPatternId}
              onChange={setFilterPatternId}
            >
              {patterns.map(pattern => (
                <Option key={pattern.id} value={pattern.id}>
                  {pattern.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col>
            <Button
              onClick={() => {
                setFilterUserType(undefined);
                setFilterPatternId(undefined);
              }}
            >
              清除筛选
            </Button>
          </Col>
        </Row>
      </Card>

      <Row gutter={16}>
        <Col span={16}>
          <Card title="工作日历">
            <Calendar
              value={selectedDate}
              onSelect={setSelectedDate}
              dateCellRender={dateCellRender}
              monthCellRender={monthCellRender}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card
            title={
              <Space>
                <CalendarOutlined />
                {selectedDate.format('YYYY年MM月DD日')} 工作安排
              </Space>
            }
          >
            {selectedDateSchedules.length === 0 ? (
              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                <UserOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>当日暂无工作安排</div>
              </div>
            ) : (
              <div>
                {selectedDateSchedules.map(scheduling => (
                  <Card
                    key={scheduling.id}
                    size="small"
                    style={{ marginBottom: '12px', cursor: 'pointer' }}
                    onClick={() => onSchedulingClick(scheduling)}
                    hoverable
                  >
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text strong>{scheduling.name}</Text>
                        <Tag color={
                          scheduling.userType === 1 ? 'blue' :
                          scheduling.userType === 2 ? 'orange' : 'green'
                        }>
                          {USER_TYPE_MAP[scheduling.userType]}
                        </Tag>
                      </div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        <div>工号: {scheduling.employeeNumber}</div>
                        <div>手机: {scheduling.phone}</div>
                        {scheduling.workPattern && (
                          <div>
                            工作时间: {minutesToTime(scheduling.workPattern.workStartMinute1)} - {minutesToTime(scheduling.workPattern.workEndMinute1)}
                          </div>
                        )}
                      </div>
                      {scheduling.workPattern && (
                        <div style={{ fontSize: '12px', color: '#999' }}>
                          工作模式: {scheduling.workPattern.name} ({scheduling.workPattern.workPatternType === 1 ? '礼拜制' : '连续制'})
                        </div>
                      )}
                    </Space>
                  </Card>
                ))}
              </div>
            )}
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default WorkCalendar;

import React from 'react';
import { ProTable, ProColumns, ActionType } from '@ant-design/pro-components';
import {Tag, Space, Button, Badge, Descriptions, Modal, message} from 'antd';
import {EditOutlined, DeleteOutlined, ClockCircleOutlined, PoweroffOutlined} from '@ant-design/icons';
import {listUserByPageUsingPost} from "@/services/logistics-backend-api/author/user/userController";
import {
  listUsingPost2,
  updateStatusUsingPost1
} from "@/services/logistics-backend-api/base/pattern/workPatternController";
import {updateStatusUsingPost} from "@/services/logistics-backend-api/base/truck/truckController";
import {WORK_DAY_STATUS_MAP, WORK_PATTERN_TYPE_MAP, USER_TYPE_MAP} from "@/constants";

interface WorkPatternManagementProps {
  patterns: WORK_PATTERN_API.WorkPattern[];
  onEditPattern: (pattern: WORK_PATTERN_API.WorkPattern) => void;
  onDeletePattern: (patternId?: number) => void;
  actionRef?: React.RefObject<ActionType>;
}

const WorkPatternManagement: React.FC<WorkPatternManagementProps> = ({
  patterns,
  onEditPattern,
  onDeletePattern,
  actionRef
}) => {

  const handleUpdateStatus = async (row: WORK_PATTERN_API.WorkPattern)=>{
    Modal.confirm({
      title: `工作状态${row?.status === 1 ? '禁用' : '启用'}`,
      content:  (
        <div>
          {`确定要${row?.status === 1 ? '禁用' : '启用'} "${row?.name}" 的工作模式状态吗？`}
          {row?.status == 1 && (
            <div>
              工作模式禁用需要注意⚠️:该工作模式未绑定排班
            </div>
          )}
        </div>
      ),
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const hide = message.loading('正在修改');
        if (!row) return true;
        try {
          hide();
          await updateStatusUsingPost1({
            id: row.id,
            status: row.status === 1 ? 0 : 1
          })
          message.success('修改成功');
          actionRef?.current?.reload();
          return true;
        } catch (error: any) {
          hide();
          message.error('修改失败，' + error.message);
          return false;
        }
      },
    })
  }
  // 将分钟数转换为时间格式
  const minutesToTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };

  // 获取工作日设置显示
  const getWorkDaysDisplay = (pattern: WORK_PATTERN_API.WorkPattern) => {
    if (pattern.workPatternType === 2) {
      return `连续工作${pattern.workDayNum}天，休息${pattern.restDayNum}天`;
    }

    const days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'];
    const dayNames = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
    const workDays: string[] = [];
    const restDays: string[] = [];

    days.forEach((day, index) => {
      const status = pattern[day as keyof  WORK_PATTERN_API.WorkPattern] as number;
      if (status === 1) {
        workDays.push(dayNames[index]);
      } else if (status === 2) {
        restDays.push(dayNames[index]);
      }
    });

    return (
      <div>
        {workDays.length > 0 && (
          <div>
            <Tag color="green">工作日</Tag>
            {workDays.join('、')}
          </div>
        )}
        {restDays.length > 0 && (
          <div style={{ marginTop: '4px' }}>
            <Tag color="red">休息日</Tag>
            {restDays.join('、')}
          </div>
        )}
      </div>
    );
  };

  const columns: ProColumns<WORK_PATTERN_API.WorkPattern>[] = [
    {
      title: '工作模式名称',
      dataIndex: 'name',
      render: (_, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{record.name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            ID: {record.id}
          </div>
        </div>
      )
    },
    {
      title: '适用人员',
      dataIndex: 'userType',
      valueEnum: {
        1: { text: '员工', status: 'Default' },
        2: { text: '快递员', status: 'Processing' },
        3: { text: '司机', status: 'Success' }
      },
      render: (_, record) => {
        const colors = { 1: 'blue', 2: 'orange', 3: 'green' };
        return (
          <Tag color={colors[record.userType]}>
            {USER_TYPE_MAP[record.userType]}
          </Tag>
        );
      }
    },
    {
      title: '工作模式类型',
      dataIndex: 'workPatternType',
      valueEnum: {
        1: { text: '礼拜制', status: 'Default' },
        2: { text: '连续制', status: 'Processing' }
      },
      render: (_, record) => (
        <Tag color={record.workPatternType === 1 ? 'blue' : 'purple'}>
          {WORK_PATTERN_TYPE_MAP[record.workPatternType]}
        </Tag>
      )
    },
    {
      title: '工作时间',
      dataIndex: 'workTime',
      hideInSearch: true,
      render: (_, record) => (
        <Space>
          <ClockCircleOutlined />
          <span>
            {minutesToTime(record.workStartMinute1)} - {minutesToTime(record.workEndMinute1)}
          </span>
          <Tag color="blue">
            {Math.round((record.workEndMinute1 - record.workStartMinute1) / 60 * 10) / 10}小时
          </Tag>
        </Space>
      )
    },
    {
      title: '工作安排',
      dataIndex: 'workSchedule',
      hideInSearch: true,
      render: (_, record) => getWorkDaysDisplay(record)
    },
    {
      title: '状态',
      dataIndex: 'status',
      valueEnum: {
        1: { text: '正常使用', status: 'Success' },
        0: { text: '停用', status: 'Error' }
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created',
      valueType: 'dateTime',
      hideInSearch: true
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined />}
          onClick={() => onEditPattern(record)}
        >
          编辑
        </Button>,
        <Button
          key="status"
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined />}
          onClick={() => onDeletePattern(record.id)}
        >
          删除
        </Button>,

        <Button
          key="delete"
          type="link"
          size="small"
          danger={record.status === 1}
          icon={< PoweroffOutlined/>}
          onClick={() => {handleUpdateStatus(record)}}
        >
          {record.status === 1 ? '禁用' : '启用'}
        </Button>
      ]
    }
  ];

  return (
    <ProTable<WORK_PATTERN_API.WorkPattern>
      rowKey={(record) => record.id || Math.random().toString()}
      actionRef={actionRef}
      columns={columns}
      request={async (params, sort, filter) => {
        console.log("查询条件为:", params);
        //拿取排序字段
        const sortField = Object.keys(sort)?.[0];
        //拿取排序方式 ascend/descend
        const sortOrder = sort?.[sortField] ?? undefined;
        const {data, code} = await listUsingPost2({
          ...params,
          //@ts-ignore
          sortField,
          sortOrder,
          ...filter,
        } as WORK_PATTERN_API.PatternQueryRequest);
        return {
          success: code === 0,
          data: data?.records || [],
          total: Number(data?.total) || 0,
        };
      }}
      search={{
        labelWidth: 'auto'
      }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true
      }}
      headerTitle="工作模式管理"
      toolBarRender={false}
    />
  );
};

export default WorkPatternManagement;

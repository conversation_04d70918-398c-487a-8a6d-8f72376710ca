import React from 'react';
import {ProTable, ProColumns, ActionType} from '@ant-design/pro-components';
import {Tag, Space, Typography, Avatar, Button, Descriptions} from 'antd';
import {EditOutlined, DeleteOutlined, UserOutlined, PhoneOutlined} from '@ant-design/icons';
import dayjs from 'dayjs';
import {listUsingPost} from "@/services/logistics-backend-api/base/scheduling/workSchedulingController";
import {USER_TYPE_MAP, WORK_DAY_STATUS_MAP} from "@/constants";

interface WorkSchedulingManagementProps {
  patterns: WORK_SCHEDULING_API.WorkSchedulingVO[];
  onEditScheduling: (scheduling: WORK_SCHEDULING_API.WorkSchedulingVO) => void;
  onDeleteScheduling: (schedulingId: number) => void;
  actionRef?: React.RefObject<ActionType>;
}

const WorkSchedulingManagement: React.FC<WorkSchedulingManagementProps> = ({
                                                                             actionRef,
                                                                             patterns,
                                                                             onEditScheduling,
                                                                             onDeleteScheduling
                                                                           }) => {
  // 将分钟数转换为时间格式
  const minutesToTime = (minutes: number): string => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  };


  const columns: ProColumns<WORK_SCHEDULING_API.WorkSchedulingVO>[] = [
    {
      title: '员工信息',
      dataIndex: 'name',
      render: (_, record) => (
        <Space>
          <Avatar size="large" icon={<UserOutlined/>}>
            {record.name?.charAt(0)}
          </Avatar>
          <div>
            <div style={{fontWeight: 'bold'}}>{record.name}</div>
            <div style={{fontSize: '12px', color: '#666'}}>
              账号: {record.employeeNumber}
            </div>
            <div style={{fontSize: '12px', color: '#666'}}>
              <PhoneOutlined/> {record.phone}
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '员工类型',
      dataIndex: 'userType',
      valueEnum: {
        1: {text: '员工', status: 'Default'},
        2: {text: '快递员', status: 'Processing'},
        3: {text: '司机', status: 'Success'}
      },
      render: (_, record) => {
        const colors = {1: 'blue', 2: 'orange', 3: 'green'};
        return (
          <Tag color={colors[record.userType]}>
            {USER_TYPE_MAP[record.userType]}
          </Tag>
        );
      }
    },
    {
      title: '用户ID',
      dataIndex: 'userId',
      hideInTable: true,
      hideInSearch: true
    },
    {
      title: '员工账号',
      dataIndex: 'employeeNumber',
      hideInTable: true
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      hideInTable: true
    },
    {
      title: '工作模式',
      dataIndex: 'workPatternId',
      render: (_, record) => (
        //如果workPatternName为null那么显示未设置如果有值直接显示
        record.workPatternName ? <Tag color="default" >{record.workPatternName}</Tag> : <Tag color="default">未设置</Tag>
      ),
      valueEnum: patterns.reduce((acc, pattern) => {
        acc[pattern.id as number] = { text: pattern.name as string};
        return acc;
      }, {} as Record<number, { text: string }>)
    },
    {
      title: '状态',
      dataIndex: 'state',
      valueEnum: {
        1: {text: '正常', status: 'Success'},
        2: {text: '停用', status: 'Error'}
      }
    },
    {
      title: '操作',
      valueType: 'option',
      render: (_, record) => [
        <Button
          key="edit"
          type="link"
          size="small"
          icon={<EditOutlined/>}
          onClick={() => onEditScheduling(record)}
        >
          编辑
        </Button>,
        <Button
          key="delete"
          type="link"
          size="small"
          danger
          icon={<DeleteOutlined/>}
          onClick={() => onDeleteScheduling(record.id as number)}
        >
          删除
        </Button>
      ]
    }
  ];

  return (
    <ProTable<WORK_SCHEDULING_API.WorkSchedulingVO>
      columns={columns}
      actionRef={actionRef}
      rowKey={(record) => record.id || Math.random().toString()}
      search={{
        labelWidth: 'auto'
      }}
      pagination={{
        pageSize: 10,
        showSizeChanger: true
      }}
      request={async (params, sort, filter) => {
        console.log("查询条件为:", params);
        const sortField = Object.keys(sort)?.[0];
        const sortOrder = sort?.[sortField] ?? undefined;
        const {data, code} = await listUsingPost({
          ...params,
          sortField,
          sortOrder,
          ...filter,
        } as WORK_SCHEDULING_API.WorkSchedulingQueryRequest)

        return {
          success: code === 0,
          data: data?.records || [],
          total: Number(data?.total) || 0,
        };
      }}
      headerTitle="员工排班管理"
      toolBarRender={false}
      // expandable={{
      //   expandedRowRender: (record) => (
      //     <Descriptions column={2} size="small">
      //       <Descriptions.Item label="创建者">用户{record.creater}</Descriptions.Item>
      //       <Descriptions.Item label="更新者">用户{record.updater}</Descriptions.Item>
      //       <Descriptions.Item label="创建时间">{record.created}</Descriptions.Item>
      //       <Descriptions.Item label="更新时间">{record.updated}</Descriptions.Item>
      //       {record.workPatternType === 1 && (
      //         <>
      //           <Descriptions.Item label="周一">{WORK_DAY_STATUS_MAP[record.monday]}</Descriptions.Item>
      //           <Descriptions.Item label="周二">{WORK_DAY_STATUS_MAP[record.tuesday]}</Descriptions.Item>
      //           <Descriptions.Item label="周三">{WORK_DAY_STATUS_MAP[record.wednesday]}</Descriptions.Item>
      //           <Descriptions.Item label="周四">{WORK_DAY_STATUS_MAP[record.thursday]}</Descriptions.Item>
      //           <Descriptions.Item label="周五">{WORK_DAY_STATUS_MAP[record.friday]}</Descriptions.Item>
      //           <Descriptions.Item label="周六">{WORK_DAY_STATUS_MAP[record.saturday]}</Descriptions.Item>
      //           <Descriptions.Item label="周日">{WORK_DAY_STATUS_MAP[record.sunday]}</Descriptions.Item>
      //         </>
      //       )}
      //       {record.workPatternType === 2 && (
      //         <>
      //           <Descriptions.Item label="连续工作天数">{record.workDayNum}天</Descriptions.Item>
      //           <Descriptions.Item label="休息天数">{record.restDayNum}天</Descriptions.Item>
      //         </>
      //       )}
      //     </Descriptions>
      //   )
      // }}
    />
  );
};

export default WorkSchedulingManagement;

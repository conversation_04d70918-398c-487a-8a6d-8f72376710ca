// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** addRole POST /api/role/add */
export async function addRoleUsingPost(
  body: API.RoleAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/api/role/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** deleteRole DELETE /api/role/delete */
export async function deleteRoleUsingDelete(
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/role/delete", {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** listRole GET /api/role/list */
export async function listRoleUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listRoleUsingGETParams,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageRole_>("/api/role/list", {
    method: "GET",
    params: {
      ...params,
    },
    ...(options || {}),
  });
}

/** listRoleVO GET /api/role/listVO */
export async function listRoleVoUsingGet(options?: { [key: string]: any }) {
  return request<API.ResultListRoleVO_>("/api/role/listVO", {
    method: "GET",
    ...(options || {}),
  });
}

/** updateRole POST /api/role/update */
export async function updateRoleUsingPost(
  body: API.RoleUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/role/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

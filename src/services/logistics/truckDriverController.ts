// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** listQueryTruckDriver POST /api/truckDriver/list */
export async function listQueryTruckDriverUsingPost(
  body: API.UserQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageDriverVO_>("/api/truckDriver/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** updateTruckDriver POST /api/truckDriver/update */
export async function updateTruckDriverUsingPost(
  body: API.UserUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/truckDriver/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

declare namespace API {
  type DeleteRequest = {
    id?: number;
  };

  type DriverVO = {
    account?: string;
    id?: number;
    mobile?: string;
    name?: string;
    orgId?: number;
    orgName?: string;
    orgTreeIds?: string[];
    status?: number;
  };

  type getByIdUsingGETParams = {
    /** id */
    id: number;
  };

  type getByTruckIdUsingGETParams = {
    /** id */
    id: number;
  };

  type getByUserIdUsingGETParams = {
    /** id */
    id: number;
  };

  type listByUserTypeUsingGETParams = {
    /** userType */
    userType: number;
  };

  type PageDriverVO_ = {
    current?: number;
    pages?: number;
    records?: DriverVO[];
    size?: number;
    total?: number;
  };

  type PageTruckType_ = {
    current?: number;
    pages?: number;
    records?: TruckType[];
    size?: number;
    total?: number;
  };

  type PageTruckVO_ = {
    current?: number;
    pages?: number;
    records?: TruckVO[];
    size?: number;
    total?: number;
  };

  type PageWorkPattern_ = {
    current?: number;
    pages?: number;
    records?: WorkPattern[];
    size?: number;
    total?: number;
  };

  type PageWorkSchedulingVO_ = {
    current?: number;
    pages?: number;
    records?: WorkSchedulingVO[];
    size?: number;
    total?: number;
  };

  type PatternAddRequest = {
    friday?: number;
    monday?: number;
    name?: string;
    restDayNum?: number;
    saturday?: number;
    status?: number;
    sunday?: number;
    thursday?: number;
    tuesday?: number;
    userType?: number;
    wednesday?: number;
    workDayNum?: number;
    workEndMinute1?: number;
    workPatternType?: number;
    workStartMinute1?: number;
  };

  type PatternEditRequest = {
    friday?: number;
    id?: number;
    monday?: number;
    name?: string;
    restDayNum?: number;
    saturday?: number;
    status?: number;
    sunday?: number;
    thursday?: number;
    tuesday?: number;
    userType?: number;
    wednesday?: number;
    workDayNum?: number;
    workEndMinute1?: number;
    workPatternType?: number;
    workStartMinute1?: number;
  };

  type PatternQueryRequest = {
    current?: number;
    name?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    userType?: number;
    workPatternType?: number;
  };

  type ResultBoolean_ = {
    code?: number;
    data?: boolean;
    description?: string;
    message?: string;
  };

  type ResultListTruckTypeVO_ = {
    code?: number;
    data?: TruckTypeVO[];
    description?: string;
    message?: string;
  };

  type ResultListWorkPatternSelectVO_ = {
    code?: number;
    data?: WorkPatternSelectVO[];
    description?: string;
    message?: string;
  };

  type ResultLong_ = {
    code?: number;
    data?: number;
    description?: string;
    message?: string;
  };

  type ResultPageDriverVO_ = {
    code?: number;
    data?: PageDriverVO_;
    description?: string;
    message?: string;
  };

  type ResultPageTruckType_ = {
    code?: number;
    data?: PageTruckType_;
    description?: string;
    message?: string;
  };

  type ResultPageTruckVO_ = {
    code?: number;
    data?: PageTruckVO_;
    description?: string;
    message?: string;
  };

  type ResultPageWorkPattern_ = {
    code?: number;
    data?: PageWorkPattern_;
    description?: string;
    message?: string;
  };

  type ResultPageWorkSchedulingVO_ = {
    code?: number;
    data?: PageWorkSchedulingVO_;
    description?: string;
    message?: string;
  };

  type ResultString_ = {
    code?: number;
    data?: string;
    description?: string;
    message?: string;
  };

  type ResultTruckDriverLicenseEditRequest_ = {
    code?: number;
    data?: TruckDriverLicenseEditRequest;
    description?: string;
    message?: string;
  };

  type ResultTruckLicenseEditRequest_ = {
    code?: number;
    data?: TruckLicenseEditRequest;
    description?: string;
    message?: string;
  };

  type ResultTruckVO_ = {
    code?: number;
    data?: TruckVO;
    description?: string;
    message?: string;
  };

  type TruckAddRequest = {
    deviceGpsId?: string;
    licensePlate?: string;
    status?: number;
    truckTypeId?: number;
    workStatus?: number;
  };

  type TruckDriverLicenseEditRequest = {
    allowableType?: string;
    driverAge?: number;
    initialCertificateDate?: string;
    licenseNumber?: string;
    licenseType?: string;
    passCertificate?: string;
    pictures?: string[];
    qualificationCertificate?: string;
    userId?: number;
    validPeriod?: string;
  };

  type TruckEditRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    deviceGpsId?: string;
    id?: number;
    licensePlate?: string;
    pictures?: string[];
    truckTypeId?: number;
  };

  type TruckLicenseEditRequest = {
    allowableWeight?: number;
    engineNumber?: string;
    expirationDate?: string;
    mandatoryScrap?: string;
    overallQuality?: number;
    pictures?: string[];
    registrationDate?: string;
    transportCertificateNumber?: string;
    truckId?: number;
    validityPeriod?: string;
  };

  type TruckQueryRequest = {
    current?: number;
    licensePlate?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    truckTypeId?: number;
  };

  type TruckType = {
    allowableLoad?: number;
    allowableVolume?: number;
    created?: string;
    id?: number;
    measureHigh?: number;
    measureLong?: number;
    measureWidth?: number;
    name?: string;
    status?: number;
    updated?: string;
  };

  type TruckTypeAddRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    measureHigh?: number;
    measureLong?: number;
    measureWidth?: number;
    name?: string;
    status?: number;
  };

  type TruckTypeQueryRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    current?: number;
    name?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    status?: number;
  };

  type TruckTypeUpdateRequest = {
    allowableLoad?: number;
    allowableVolume?: number;
    created?: string;
    id?: number;
    measureHigh?: number;
    measureLong?: number;
    measureWidth?: number;
    name?: string;
    status?: number;
    updated?: string;
  };

  type TruckTypeVO = {
    id?: number;
    name?: string;
  };

  type TruckUpdateStatusRequest = {
    id?: number;
    status?: number;
  };

  type TruckVO = {
    allowableLoad?: number;
    allowableVolume?: number;
    deviceGpsId?: string;
    id?: number;
    licensePlate?: string;
    pictures?: string[];
    status?: number;
    truckTypeId?: number;
    workStatus?: number;
  };

  type UserQueryRequest = {
    current?: number;
    mobile?: string;
    name?: string;
    orgId?: number;
    pageSize?: number;
    roleType?: string;
    sortField?: string;
    sortOrder?: string;
    stationId?: number;
    status?: number;
  };

  type UserUpdateRequest = {
    id?: number;
    orgId?: number;
    stationId?: number;
    status?: number;
  };

  type WorkPattern = {
    created?: string;
    creater?: number;
    friday?: number;
    id?: number;
    isDelete?: number;
    monday?: number;
    name?: string;
    restDayNum?: number;
    saturday?: number;
    status?: number;
    sunday?: number;
    thursday?: number;
    tuesday?: number;
    updated?: string;
    updater?: number;
    userType?: number;
    wednesday?: number;
    workDayNum?: number;
    workEndMinute1?: number;
    workPatternType?: number;
    workStartMinute1?: number;
  };

  type WorkPatternSelectVO = {
    id?: number;
    name?: string;
  };

  type WorkPatternUpdateStatusRequest = {
    id?: number;
    status?: number;
  };

  type WorkSchedulingAddRequest = {
    agencyId?: number;
    employeeNumber?: string;
    name?: string;
    phone?: string;
    state?: number;
    userId?: number;
    userType?: number;
    workPatternId?: number;
  };

  type WorkSchedulingQueryRequest = {
    agencyId?: number;
    current?: number;
    employeeNumber?: string;
    name?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    workPatternId?: number;
  };

  type WorkSchedulingVO = {
    employeeNumber?: string;
    id?: number;
    name?: string;
    phone?: string;
    state?: number;
    userId?: number;
    userType?: number;
    workPatternId?: number;
    workPatternName?: string;
  };
}

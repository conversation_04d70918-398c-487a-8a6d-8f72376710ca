// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** addCoreStation POST /api/coreOrg/add */
export async function addCoreStationUsingPost(
  body: API.CoreOrgAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/api/coreOrg/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** deleteCoreOrg DELETE /api/coreOrg/delete/${param0} */
export async function deleteCoreOrgUsingDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteCoreOrgUsingDELETEParams,
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultLong_>(`/api/coreOrg/delete/${param0}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    params: { ...queryParams },
    data: body,
    ...(options || {}),
  });
}

/** listQueryAllCoreOrgTree POST /api/coreOrg/list */
export async function listQueryAllCoreOrgTreeUsingPost(
  body: API.CoreOrgQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultListCoreOrgTreeVo_>("/api/coreOrg/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** listAllCoreOrgTree POST /api/coreOrg/list/tree */
export async function listAllCoreOrgTreeUsingPost(options?: {
  [key: string]: any;
}) {
  return request<API.ResultListCoreOrgTreeVo_>("/api/coreOrg/list/tree", {
    method: "POST",
    ...(options || {}),
  });
}

/** updateCoreOrg POST /api/coreOrg/update */
export async function updateCoreOrgUsingPost(
  body: API.CoreOrgUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/coreOrg/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

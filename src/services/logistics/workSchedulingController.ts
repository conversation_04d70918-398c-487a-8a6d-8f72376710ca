// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** add POST /api/workScheduling/add */
export async function addUsingPost2(
  body: API.WorkSchedulingAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/workScheduling/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** list POST /api/workScheduling/list */
export async function listUsingPost3(
  body: API.WorkSchedulingQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageWorkSchedulingVO_>("/api/workScheduling/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** addCoreStation POST /api/coreStation/add */
export async function addCoreStationUsingPost1(
  body: API.CoreStationAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/api/coreStation/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** deleteCoreStation DELETE /api/coreStation/delete/${param0} */
export async function deleteCoreStationUsingDelete(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.deleteCoreStationUsingDELETEParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultString_>(`/api/coreStation/delete/${param0}`, {
    method: "DELETE",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** getCoreStationById GET /api/coreStation/get/${param0} */
export async function getCoreStationByIdUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getCoreStationByIdUsingGETParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultCoreStation_>(`/api/coreStation/get/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** getIdByName GET /api/coreStation/getIdByName/${param0} */
export async function getIdByNameUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getIdByNameUsingGETParams,
  options?: { [key: string]: any }
) {
  const { name: param0, ...queryParams } = params;
  return request<API.ResultLong_>(`/api/coreStation/getIdByName/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** listAllCoreStation POST /api/coreStation/list */
export async function listAllCoreStationUsingPost(
  body: API.CoreStationQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.PageCoreStation_>("/api/coreStation/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** listAllCoreStationVO GET /api/coreStation/list/vo */
export async function listAllCoreStationVoUsingGet(options?: {
  [key: string]: any;
}) {
  return request<API.ResultListCoreStationVO_>("/api/coreStation/list/vo", {
    method: "GET",
    ...(options || {}),
  });
}

/** updateCoreStation POST /api/coreStation/update */
export async function updateCoreStationUsingPost(
  body: API.CoreStationUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/coreStation/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

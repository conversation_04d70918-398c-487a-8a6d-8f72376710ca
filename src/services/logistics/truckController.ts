// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** addTruck POST /api/truck/add */
export async function addTruckUsingPost(
  body: API.TruckAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/api/truck/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** editTruckInfo POST /api/truck/editTruck */
export async function editTruckInfoUsingPost(
  body: API.TruckEditRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean_>("/api/truck/editTruck", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** getById GET /api/truck/getById/${param0} */
export async function getByIdUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getByIdUsingGETParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultTruckVO_>(`/api/truck/getById/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}

/** list POST /api/truck/list */
export async function listUsingPost(
  body: API.TruckQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageTruckVO_>("/api/truck/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** remove DELETE /api/truck/remove */
export async function removeUsingDelete(
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/truck/remove", {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** updateStatus POST /api/truck/updateStatus */
export async function updateStatusUsingPost(
  body: API.TruckUpdateStatusRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/truck/updateStatus", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** add POST /api/truckType/add */
export async function addUsingPost(
  body: API.TruckTypeAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/api/truckType/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** delete DELETE /api/truckType/delete */
export async function deleteUsingDelete(
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/truckType/delete", {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** getAllVO GET /api/truckType/getAll */
export async function getAllVoUsingGet(options?: { [key: string]: any }) {
  return request<API.ResultListTruckTypeVO_>("/api/truckType/getAll", {
    method: "GET",
    ...(options || {}),
  });
}

/** list POST /api/truckType/list */
export async function listUsingPost1(
  body: API.TruckTypeQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageTruckType_>("/api/truckType/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** update POST /api/truckType/update */
export async function updateUsingPost(
  body: API.TruckTypeUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean_>("/api/truckType/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

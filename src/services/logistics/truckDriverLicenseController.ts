// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** edit POST /api/truckDriverLicense/edit */
export async function editUsingPost(
  body: API.TruckDriverLicenseEditRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/truckDriverLicense/edit", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** getByUserId GET /api/truckDriverLicense/getByUserId/${param0} */
export async function getByUserIdUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.getByUserIdUsingGETParams,
  options?: { [key: string]: any }
) {
  const { id: param0, ...queryParams } = params;
  return request<API.ResultTruckDriverLicenseEditRequest_>(
    `/api/truckDriverLicense/getByUserId/${param0}`,
    {
      method: "GET",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

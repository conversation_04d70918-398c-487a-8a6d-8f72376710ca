// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";

/** add POST /api/workPattern/add */
export async function addUsingPost1(
  body: API.PatternAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/api/workPattern/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** addEmployeeScheduling POST /api/workPattern/addEmployeeScheduling */
export async function addEmployeeSchedulingUsingPost(
  body: API.WorkSchedulingAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/workPattern/addEmployeeScheduling", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** delete POST /api/workPattern/delete */
export async function deleteUsingPost(
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/workPattern/delete", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** edit POST /api/workPattern/edit */
export async function editUsingPost2(
  body: API.PatternEditRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/workPattern/edit", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** list POST /api/workPattern/list */
export async function listUsingPost2(
  body: API.PatternQueryRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultPageWorkPattern_>("/api/workPattern/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** listByUserType GET /api/workPattern/listByUserType/${param0} */
export async function listByUserTypeUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: API.listByUserTypeUsingGETParams,
  options?: { [key: string]: any }
) {
  const { userType: param0, ...queryParams } = params;
  return request<API.ResultListWorkPatternSelectVO_>(
    `/api/workPattern/listByUserType/${param0}`,
    {
      method: "GET",
      params: { ...queryParams },
      ...(options || {}),
    }
  );
}

/** updateStatus POST /api/workPattern/updateStatus */
export async function updateStatusUsingPost1(
  body: API.WorkPatternUpdateStatusRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/api/workPattern/updateStatus", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

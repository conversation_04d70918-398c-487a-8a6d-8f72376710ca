declare namespace WORK_PATTERN_API {
  type PatternAddRequest = {
    friday?: number;
    monday?: number;
    name?: string;
    restDayNum?: number;
    saturday?: number;
    status?: number;
    sunday?: number;
    thursday?: number;
    tuesday?: number;
    userType?: number;
    wednesday?: number;
    workDayNum?: number;
    workEndMinute1?: number;
    workPatternType?: number;
    workStartMinute1?: number;
  };
  type PatternEditRequest = {
    friday?: number;
    id?: number;
    monday?: number;
    name?: string;
    restDayNum?: number;
    saturday?: number;
    status?: number;
    sunday?: number;
    thursday?: number;
    tuesday?: number;
    userType?: number;
    wednesday?: number;
    workDayNum?: number;
    workEndMinute1?: number;
    workPatternType?: number;
    workStartMinute1?: number;
  };

  type PatternQueryRequest = {
    current?: number;
    name?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    userType?: number;
    workPatternType?: number;
  };

  type ResultPageWorkPattern_ = {
    code?: number;
    data?: PageWorkPattern_;
    description?: string;
    message?: string;
  };

  type PageWorkPattern_ = {
    current?: number;
    pages?: number;
    records?: WorkPattern[];
    size?: number;
    total?: number;
  };
  type WorkPattern = {
    id?: number;
    name?: string;
    friday: 0 | 1 | 2;
    monday: 0 | 1 | 2; //0:未填，1:上班，2：不上班
    saturday: 0 | 1 | 2;
    status: 0 | 1;
    sunday: 0 | 1 | 2;
    thursday: 0 | 1 | 2;
    tuesday: 0 | 1 | 2;
    userType: 1 | 2 | 3;  //1.员工 2:快递员 3:司机
    wednesday: 0 | 1 | 2;
    restDayNum?: number; // 连续制工作天数
    workDayNum?: number; // 连续制休息天数
    workPatternType: 1 | 2; //1礼拜制 2.连续制
    workStartMinute1: number; //结束工作分钟数
    workEndMinute1: number; // 开始工作分钟数
    updated?: string;
    created?: string;
    updater?: number;
    creater?: number;
  };

  type WorkPatternUpdateStatusRequest = {
    id?: number;
    status?: number;
  };

  type listByUserTypeUsingGETParams = {
    /** userType */
    userType: number;
  };
  type ResultListWorkPatternSelectVO_ = {
    code?: number;
    data?: WorkPatternSelectVO[];
    description?: string;
    message?: string;
  };

  type WorkPatternSelectVO = {
    id?: number;
    name?: string;
  };
}

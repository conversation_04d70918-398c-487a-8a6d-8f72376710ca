// @ts-ignore
/* eslint-disable */
import { request } from "@umijs/max";


export async function listUsingPost(
  body: WORK_SCHEDULING_API.WorkSchedulingQueryRequest,
  options?: { [key: string]: any }
) {
  return request<WORK_SCHEDULING_API.ResultPageWorkSchedulingVO_>("/base/workScheduling/list", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** add POST /api/workScheduling/add */
export async function addUsingPost2(
  body: WORK_SCHEDULING_API.WorkSchedulingAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/base/workScheduling/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

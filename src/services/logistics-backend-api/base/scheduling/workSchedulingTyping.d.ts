
declare namespace WORK_SCHEDULING_API {
  type WorkSchedulingQueryRequest = {
    agencyId?: number;
    current?: number;
    employeeNumber?: string;
    name?: string;
    pageSize?: number;
    sortField?: string;
    sortOrder?: string;
    workPatternId?: number;
  };

  type ResultPageWorkSchedulingVO_ = {
    code?: number;
    data?: PageWorkSchedulingVO_;
    description?: string;
    message?: string;
  };
  type PageWorkSchedulingVO_ = {
    current?: number;
    pages?: number;
    records?: WorkSchedulingVO[];
    size?: number;
    total?: number;
  };
  type WorkSchedulingVO = {
    employeeNumber?: string;
    id?: number;
    name?: string;
    phone?: string;
    state?: number;
    userId?: number;
    userType: 1 | 2 | 3;
    workPatternId?: number;
    workPatternName?: string;
    workContinueStartTime?: string; // 连续制开始时间
  };

  type WorkSchedulingAddRequest = {
    agencyId?: number;
    employeeNumber?: string;
    name?: string;
    phone?: string;
    state?: number;
    userId?: number;
    userType?: number;
    workPatternId?: number;
    workContinueStartTime?: string; // 连续制开始时间
  };
}

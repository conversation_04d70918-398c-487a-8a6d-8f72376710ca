// @ts-ignore
/* eslint-disable */

import {request} from "@umijs/max";

/** addUser POST /api/user/add */
export async function addUserUsingPost(
  body: USER_API.UserAddRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultLong_>("/author/user/add", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}


/** getLoginUser GET /api/user/get/login */
export async function getLoginUserUsingGet(options?: { [key: string]: any }) {
  return request<USER_API.ResultLoginUserVO_>("/author/user/get/login", {
    method: "GET",
    ...(options || {}),
  });
}

/** userLogin POST /api/user/login */
export async function userLoginUsingPost(
  body: USER_API.UserLoginRequest,
  options?: { [key: string]: any }
) {
  return request<USER_API.ResultLoginUserVO_>("/author/user/login", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Client-Type": "admin"
    },
    data: body,
    ...(options || {}),
  });
}

/** listUserByPage POST /api/user/list/page */
export async function listUserByPageUsingPost(
  body: USER_API.UserQueryRequest,
  options?: { [key: string]: any }
) {
  return request<USER_API.ResultPageUserVO_>("/author/user/list/page", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** updateUser POST /api/user/update */
export async function updateUserUsingPost(
  body: USER_API.UserUpdateRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultBoolean_>("/author/user/update", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  })
};

/** logout GET /api/user/logout */
export async function logoutUsingGet(options?: { [key: string]: any }) {
  return request<API.ResultString_>("/author/user/logout", {
      method: "GET",
      ...(options || {}),
    }
  );
}


/** deleteUser DELETE /api/user/delete */
export async function deleteUserUsingDelete(
  body: API.DeleteRequest,
  options?: { [key: string]: any }
) {
  return request<API.ResultString_>("/author/user/delete", {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
    },
    data: body,
    ...(options || {}),
  });
}

/** getAccount GET /api/user/getAccount/${param0} */
export async function getAccountUsingGet(
  // 叠加生成的Param类型 (非body参数swagger默认没有生成对象)
  params: USER_API.getAccountUsingGETParams,
  options?: { [key: string]: any }
) {
  const { account: param0, ...queryParams } = params;
  return request<USER_API.ResultUserVO_>(`/author/user/getAccount/${param0}`, {
    method: "GET",
    params: { ...queryParams },
    ...(options || {}),
  });
}
